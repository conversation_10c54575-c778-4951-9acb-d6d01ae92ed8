* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #00d4aa;
    --secondary-color: #667eea;
    --accent-color: #ff6b6b;
    --warning-color: #feca57;
    --success-color: #48dbfb;
    --error-color: #ff3838;
    --glass-bg: rgba(255, 255, 255, 0.08);
    --glass-border: rgba(255, 255, 255, 0.15);
    --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
    --shadow-heavy: 0 20px 60px rgba(0, 0, 0, 0.4);
    --shadow-glow: 0 0 40px rgba(0, 212, 170, 0.3);
    --border-radius: 20px;
    --border-radius-large: 30px;
    --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    /* 渐变色 */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #00d4aa 0%, #48dbfb 100%);
    --gradient-warning: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
    --gradient-error: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
}

body {
    font-family: 'Orbitron', 'Inter', 'Arial', sans-serif;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    min-height: 100vh;
    color: white;
    overflow-x: hidden;
    position: relative;
}

/* 增强的动画背景 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.4) 0%, transparent 60%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.4) 0%, transparent 60%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 60%),
        radial-gradient(circle at 60% 70%, rgba(46, 204, 113, 0.2) 0%, transparent 50%);
    animation: backgroundShift 25s ease-in-out infinite;
    z-index: -1;
}

/* 添加粒子背景效果 */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.1), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.1), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.1), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.1), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(255, 255, 255, 0.1), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: sparkle 20s linear infinite;
    z-index: -1;
    opacity: 0.6;
}

@keyframes backgroundShift {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        filter: hue-rotate(0deg);
    }
    33% {
        transform: scale(1.05) rotate(120deg);
        filter: hue-rotate(60deg);
    }
    66% {
        transform: scale(1.1) rotate(240deg);
        filter: hue-rotate(120deg);
    }
}

@keyframes sparkle {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(180deg); }
}

/* 菜单屏幕 */
.menu-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
    transition: all 0.5s ease;
}

.menu-screen.hidden {
    opacity: 0;
    pointer-events: none;
    transform: scale(0.9);
}

.menu-container {
    text-align: center;
    background: var(--glass-bg);
    padding: 50px 40px;
    border-radius: var(--border-radius);
    backdrop-filter: blur(20px) saturate(180%);
    box-shadow:
        var(--shadow-heavy),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    max-width: 650px;
    width: 90%;
    position: relative;
    overflow: hidden;
    transition: var(--transition);
}

.menu-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.menu-container:hover::before {
    left: 100%;
}

.menu-container:hover {
    transform: translateY(-5px);
    box-shadow:
        0 25px 70px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1);
}

.game-title {
    font-size: 3.5em;
    font-weight: 900;
    margin-bottom: 50px;
    background: linear-gradient(45deg, #ff6b6b, #00d4aa, #667eea, #feca57, #ff9ff3, #48dbfb);
    background-size: 800% 800%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 5s ease-in-out infinite;
    position: relative;
    letter-spacing: 3px;
    text-transform: uppercase;
    font-family: 'Orbitron', sans-serif;
    filter: drop-shadow(0 0 30px rgba(255, 255, 255, 0.2));
}

.game-title::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease-in-out infinite reverse;
    opacity: 0.3;
    z-index: -1;
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
        transform: scale(1);
    }
    25% {
        background-position: 100% 50%;
        transform: scale(1.02);
    }
    50% {
        background-position: 200% 50%;
        transform: scale(1);
    }
    75% {
        background-position: 300% 50%;
        transform: scale(1.02);
    }
}

/* 菜单按钮 */
.menu-buttons, .mode-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

.multiplayer-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

.menu-btn, .mode-btn {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 20px 40px;
    border-radius: 60px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow:
        0 12px 35px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(15px);
    letter-spacing: 0.8px;
    text-transform: uppercase;
    font-family: 'Orbitron', sans-serif;
}

.menu-btn::before, .mode-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-btn::after, .mode-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translate(-50%, -50%);
    border-radius: 50%;
}

.menu-btn:hover::before, .mode-btn:hover::before {
    left: 100%;
}

.menu-btn:hover::after, .mode-btn:hover::after {
    width: 300px;
    height: 300px;
}

.menu-btn:hover, .mode-btn:hover {
    transform: translateY(-8px) scale(1.03);
    box-shadow:
        0 25px 60px rgba(102, 126, 234, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.2),
        var(--shadow-glow);
    background: linear-gradient(135deg, #7c8ef0 0%, #8a5cb8 100%);
    filter: brightness(1.1);
}

.menu-btn:active, .mode-btn:active {
    transform: translateY(-4px) scale(0.98);
    transition: var(--transition-fast);
    box-shadow:
        0 15px 40px rgba(102, 126, 234, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.mode-btn {
    text-align: left;
    padding: 20px;
}

.mode-btn h3 {
    margin-bottom: 5px;
    font-size: 20px;
}

.mode-btn p {
    font-size: 14px;
    opacity: 0.8;
    font-weight: normal;
}

.back-btn {
    background: var(--gradient-error);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 30px;
    transition: var(--transition);
    box-shadow:
        0 8px 25px rgba(231, 76, 60, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    letter-spacing: 0.5px;
    text-transform: uppercase;
    position: relative;
    overflow: hidden;
}

.back-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.back-btn:hover::before {
    left: 100%;
}

.back-btn:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow:
        0 15px 40px rgba(231, 76, 60, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    filter: brightness(1.1);
}

.back-btn:active {
    transform: translateY(-2px) scale(0.98);
    transition: var(--transition-fast);
}

/* 游戏屏幕 */
.game-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 50;
    transition: all 0.5s ease;
}

.game-screen.hidden {
    opacity: 0;
    pointer-events: none;
    transform: scale(0.9);
}

.game-container {
    text-align: center;
    background: var(--glass-bg);
    padding: 30px;
    border-radius: var(--border-radius);
    backdrop-filter: blur(20px) saturate(180%);
    box-shadow:
        var(--shadow-light),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border: 1px solid var(--glass-border);
    position: relative;
    overflow: hidden;
    transition: var(--transition);
}

.game-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.03), transparent);
    animation: rotate 20s linear infinite;
    z-index: -1;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.game-header {
    margin-bottom: 20px;
}

.game-info {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 15px;
}

.info-item {
    background: var(--glass-bg);
    padding: 12px 20px;
    border-radius: 25px;
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
    box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.info-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(78, 205, 196, 0.1), transparent);
    transition: left 0.6s ease;
}

.info-item:hover::before {
    left: 100%;
}

.info-item:hover {
    transform: translateY(-2px);
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: rgba(78, 205, 196, 0.3);
}

.info-item .label {
    font-size: 13px;
    opacity: 0.9;
    margin-right: 8px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-item .value {
    font-size: 18px;
    font-weight: 700;
    color: #4ecdc4;
    transition: var(--transition);
    text-shadow: 0 0 10px rgba(78, 205, 196, 0.3);
}

/* 分数更新动画 */
@keyframes scoreUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); color: #f1c40f; }
    100% { transform: scale(1); }
}

.game-status {
    color: #f39c12;
    font-size: 16px;
    text-align: center;
    padding: 10px;
    background: rgba(243, 156, 18, 0.1);
    border-radius: 10px;
    border: 1px solid rgba(243, 156, 18, 0.3);
}

/* 游戏区域 */
.game-area {
    position: relative;
    display: inline-block;
    margin-bottom: 20px;
}

#gameCanvas {
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
    box-shadow:
        0 0 40px rgba(78, 205, 196, 0.4),
        inset 0 0 40px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

#gameCanvas::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%);
    animation: canvasShine 3s ease-in-out infinite;
    pointer-events: none;
    border-radius: 18px;
}

#gameCanvas:hover {
    box-shadow:
        0 0 60px rgba(78, 205, 196, 0.6),
        inset 0 0 40px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 0 0 1px rgba(78, 205, 196, 0.3);
    transform: scale(1.01);
}

@keyframes canvasShine {
    0%, 100% { opacity: 0; transform: translateX(-100%) skewX(-15deg); }
    50% { opacity: 1; transform: translateX(100%) skewX(-15deg); }
}

.effects-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    border-radius: 15px;
    overflow: hidden;
}

/* 粒子效果 */
.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: radial-gradient(circle, #4ecdc4, transparent);
    border-radius: 50%;
    animation: particleFloat 2s ease-out forwards;
}

@keyframes particleFloat {
    0% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    100% {
        opacity: 0;
        transform: scale(0.5) translateY(-50px);
    }
}

.score-popup {
    position: absolute;
    color: #4ecdc4;
    font-weight: bold;
    font-size: 20px;
    text-shadow: 0 0 10px #4ecdc4;
    animation: scorePopup 1s ease-out forwards;
    pointer-events: none;
}

@keyframes scorePopup {
    0% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    100% {
        opacity: 0;
        transform: scale(1.5) translateY(-30px);
    }
}

/* 游戏控制按钮 */
.game-controls {
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}

.control-button {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.control-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.control-button:hover::before {
    left: 100%;
}

.control-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.control-button:active {
    transform: translateY(0);
}

.control-button:disabled {
    background: linear-gradient(45deg, #95a5a6, #7f8c8d);
    cursor: not-allowed;
    transform: none;
}

.control-button:disabled::before {
    display: none;
}

/* 键盘提示 */
.keyboard-hints {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.hint-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 15px;
    font-size: 12px;
}

.hint-item .key {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 8px;
    border-radius: 8px;
    font-weight: bold;
    font-size: 11px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.hint-item .action {
    opacity: 0.8;
}

/* 排行榜样式 */
.leaderboard-tabs {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
}

.tab-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.tab-btn.active {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

.leaderboard-content {
    max-height: 400px;
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
}

.leaderboard-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    border-left: 4px solid #4ecdc4;
}

.leaderboard-item.rank-1 { border-left-color: #f1c40f; }
.leaderboard-item.rank-2 { border-left-color: #95a5a6; }
.leaderboard-item.rank-3 { border-left-color: #e67e22; }

.rank {
    font-size: 18px;
    font-weight: bold;
    width: 30px;
}

.player-info {
    flex: 1;
    text-align: left;
    margin-left: 15px;
}

.player-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.player-details {
    font-size: 12px;
    opacity: 0.7;
}

.score-info {
    text-align: right;
}

.score-value {
    font-size: 18px;
    font-weight: bold;
    color: #4ecdc4;
}

/* 设置界面 */
.settings-content {
    text-align: left;
    max-width: 400px;
    margin: 0 auto;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    margin-bottom: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.setting-item label {
    font-weight: bold;
}

.setting-item input, .setting-item select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    padding: 5px 10px;
    color: white;
}

/* 游戏说明 */
.instructions-content {
    text-align: left;
    max-width: 500px;
    margin: 0 auto;
}

.instruction-section {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    border-left: 4px solid #4ecdc4;
}

.instruction-section h3 {
    margin-bottom: 15px;
    color: #4ecdc4;
    font-size: 18px;
}

.instruction-section p {
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.5;
}

/* 移动端控制 */
.mobile-controls {
    display: none;
    margin-top: 20px;
}

.control-row {
    margin: 5px 0;
    display: flex;
    justify-content: center;
    gap: 10px;
}

.control-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: var(--transition);
    box-shadow:
        0 8px 25px rgba(102, 126, 234, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.control-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.control-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translate(-50%, -50%);
    border-radius: 50%;
}

.control-btn:hover::before {
    left: 100%;
}

.control-btn:hover::after {
    width: 100px;
    height: 100px;
}

.control-btn:hover {
    transform: scale(1.15);
    box-shadow:
        0 12px 35px rgba(102, 126, 234, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    background: linear-gradient(135deg, #7c8ef0 0%, #8a5cb8 100%);
}

.control-btn:active {
    transform: scale(0.9);
    transition: all 0.1s ease;
}

/* 新增动画效果 */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 20px rgba(78, 205, 196, 0.4); }
    50% { box-shadow: 0 0 40px rgba(78, 205, 196, 0.8); }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 为元素添加入场动画 */
.menu-btn {
    animation: slideInUp 0.6s ease-out;
}

.menu-btn:nth-child(1) { animation-delay: 0.1s; }
.menu-btn:nth-child(2) { animation-delay: 0.2s; }
.menu-btn:nth-child(3) { animation-delay: 0.3s; }
.menu-btn:nth-child(4) { animation-delay: 0.4s; }
.menu-btn:nth-child(5) { animation-delay: 0.5s; }
.menu-btn:nth-child(6) { animation-delay: 0.6s; }
.menu-btn:nth-child(7) { animation-delay: 0.7s; }
.menu-btn:nth-child(8) { animation-delay: 0.8s; }

.info-item {
    animation: slideInLeft 0.6s ease-out;
}

.info-item:nth-child(1) { animation-delay: 0.1s; }
.info-item:nth-child(2) { animation-delay: 0.2s; }
.info-item:nth-child(3) { animation-delay: 0.3s; }
.info-item:nth-child(4) { animation-delay: 0.4s; }
.info-item:nth-child(5) { animation-delay: 0.5s; }

/* 改进的响应式设计 */
@media (max-width: 768px) {
    .menu-container {
        padding: 40px 25px;
        margin: 15px;
        border-radius: 15px;
    }

    .game-title {
        font-size: 2.4em;
        margin-bottom: 30px;
        letter-spacing: 1px;
    }

    .game-container {
        padding: 20px;
        margin: 10px;
        border-radius: 15px;
    }

    #gameCanvas {
        width: 350px;
        height: 350px;
        border-radius: 15px;
    }

    .mobile-controls {
        display: block;
        margin-top: 25px;
    }

    .control-btn {
        width: 65px;
        height: 65px;
        font-size: 26px;
    }

    .game-info {
        flex-direction: column;
        gap: 12px;
    }

    .info-item {
        width: 100%;
        text-align: center;
        padding: 10px 18px;
    }

    .control-button {
        padding: 12px 18px;
        font-size: 14px;
        border-radius: 20px;
    }

    .leaderboard-tabs {
        flex-direction: column;
        gap: 8px;
    }

    .tab-btn {
        width: 100%;
        padding: 12px 20px;
        border-radius: 15px;
    }

    .menu-btn, .mode-btn {
        padding: 16px 30px;
        font-size: 17px;
        border-radius: 50px;
    }
}

@media (max-width: 480px) {
    .game-title {
        font-size: 1.8em;
    }

    #gameCanvas {
        width: 280px;
        height: 280px;
    }

    .menu-btn, .mode-btn {
        font-size: 16px;
        padding: 12px 20px;
    }

    .control-btn {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}

/* 重新设计的游戏结束弹窗 */
.game-over {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background:
        linear-gradient(135deg, rgba(15, 15, 35, 0.98) 0%, rgba(26, 26, 46, 0.98) 100%);
    color: white;
    padding: 50px 40px;
    border-radius: 30px;
    text-align: center;
    z-index: 1000;
    box-shadow:
        0 30px 80px rgba(0, 0, 0, 0.8),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(25px) saturate(180%);
    animation: gameOverAppear 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    max-width: 500px;
    width: 90%;
    position: relative;
    overflow: hidden;
}

.game-over::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #e74c3c, #f39c12, #e74c3c);
    background-size: 200% 100%;
    animation: gradientMove 3s ease-in-out infinite;
}

.game-over::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(231, 76, 60, 0.03), transparent);
    animation: rotate 15s linear infinite;
    z-index: -1;
}

@keyframes gameOverAppear {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.7) rotateY(15deg);
        filter: blur(10px);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1) rotateY(0deg);
        filter: blur(0px);
    }
}

@keyframes gradientMove {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.game-over h2 {
    background: linear-gradient(45deg, #e74c3c, #f39c12, #e74c3c);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 30px;
    font-size: 2.8em;
    font-weight: 900;
    text-shadow: 0 0 30px rgba(231, 76, 60, 0.3);
    animation: gradientShift 3s ease-in-out infinite;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.game-over p {
    margin: 20px 0;
    font-size: 1.1em;
    opacity: 0.9;
    font-weight: 300;
}

.game-over .final-score {
    font-size: 2.2em;
    color: #4ecdc4;
    font-weight: 800;
    text-shadow: 0 0 20px rgba(78, 205, 196, 0.6);
    margin: 25px 0;
    animation: scoreGlow 2s ease-in-out infinite alternate;
}

@keyframes scoreGlow {
    0% {
        transform: scale(1);
        text-shadow: 0 0 20px rgba(78, 205, 196, 0.6);
    }
    100% {
        transform: scale(1.05);
        text-shadow: 0 0 30px rgba(78, 205, 196, 0.9);
    }
}

.game-over button {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    border: none;
    padding: 18px 35px;
    margin: 8px;
    border-radius: 50px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 10px 30px rgba(39, 174, 96, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
    letter-spacing: 0.5px;
    min-width: 160px;
}

.game-over button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.game-over button:hover::before {
    left: 100%;
}

.game-over button:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow:
        0 20px 50px rgba(39, 174, 96, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
}

.game-over button:active {
    transform: translateY(-2px) scale(0.98);
    transition: all 0.1s ease;
}

/* 重新设计的游戏统计样式 */
.game-stats {
    margin: 30px 0;
    text-align: left;
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
    border-radius: 20px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.game-stats::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #4ecdc4, #45b7d1, #4ecdc4);
    background-size: 200% 100%;
    animation: gradientMove 2s ease-in-out infinite;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 4px 0;
    position: relative;
}

.stat-row:hover {
    background: rgba(255, 255, 255, 0.05);
    padding-left: 10px;
    padding-right: 10px;
}

.stat-row:last-child {
    border-bottom: none;
}

.stat-label {
    font-size: 15px;
    opacity: 0.9;
    font-weight: 500;
    color: #ffffff;
    display: flex;
    align-items: center;
    gap: 8px;
}

.stat-label::before {
    content: '●';
    color: #4ecdc4;
    font-size: 12px;
}

.stat-value {
    font-weight: 700;
    color: #4ecdc4;
    font-size: 16px;
    text-shadow: 0 0 10px rgba(78, 205, 196, 0.4);
    background: linear-gradient(45deg, #4ecdc4, #45b7d1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-value.final-score {
    font-size: 20px;
    color: #f1c40f;
    text-shadow: 0 0 15px rgba(241, 196, 15, 0.6);
    background: linear-gradient(45deg, #f1c40f, #f39c12);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: scoreGlow 2s ease-in-out infinite alternate;
}

.game-over-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 35px;
}

.game-over-buttons button {
    width: 100%;
    animation: slideInUp 0.6s ease-out;
}

.game-over-buttons button:nth-child(1) { animation-delay: 0.1s; }
.game-over-buttons button:nth-child(2) { animation-delay: 0.2s; }
.game-over-buttons button:nth-child(3) { animation-delay: 0.3s; }

/* 暂停覆盖层 */
.pause-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.pause-content {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(26, 26, 46, 0.9));
    color: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    border: 2px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.7);
}

.pause-content h2 {
    margin-bottom: 20px;
    font-size: 2em;
    color: #f39c12;
}

.pause-content p {
    margin-bottom: 20px;
    opacity: 0.8;
}

.pause-stats {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 15px;
    margin: 20px 0;
}

.pause-stats div {
    margin: 5px 0;
    font-weight: bold;
    color: #4ecdc4;
}

.pause-content button {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 20px;
    margin: 5px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.pause-content button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* 名字输入弹窗 */
.name-input-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(26, 26, 46, 0.95));
    color: white;
    padding: 30px;
    border-radius: 20px;
    text-align: center;
    z-index: 1001;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.7);
    border: 2px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
}

.name-input-modal input {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    padding: 12px 20px;
    color: white;
    font-size: 16px;
    margin: 15px 0;
    width: 200px;
    text-align: center;
}

.name-input-modal input:focus {
    outline: none;
    border-color: #4ecdc4;
    box-shadow: 0 0 15px rgba(78, 205, 196, 0.3);
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #4ecdc4;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 成就通知 */
.achievement {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    animation: achievementSlide 3s ease-in-out;
}

@keyframes achievementSlide {
    0%, 100% { transform: translateX(100%); opacity: 0; }
    10%, 90% { transform: translateX(0); opacity: 1; }
}

/* 收集效果动画 */
@keyframes collectEffect {
    0% {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
    50% {
        transform: scale(1.5) translateY(-20px);
        opacity: 0.8;
    }
    100% {
        transform: scale(2) translateY(-40px);
        opacity: 0;
    }
}

/* 蛇身呼吸效果 */
@keyframes snakeBreath {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* 食物脉动效果 */
@keyframes foodPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* 护盾效果 */
@keyframes shieldGlow {
    0%, 100% {
        box-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 20px rgba(52, 152, 219, 0.8);
        transform: scale(1.02);
    }
}

/* 速度提升效果 */
@keyframes speedBoost {
    0%, 100% {
        box-shadow: 0 0 10px rgba(241, 196, 15, 0.5);
    }
    50% {
        box-shadow: 0 0 25px rgba(241, 196, 15, 0.9);
    }
}

/* 关卡完成庆祝效果 */
@keyframes levelComplete {
    0% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.1) rotate(5deg); }
    50% { transform: scale(1.2) rotate(-5deg); }
    75% { transform: scale(1.1) rotate(3deg); }
    100% { transform: scale(1) rotate(0deg); }
}

/* 游戏结束震动效果 */
@keyframes gameOverShake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

/* 新记录闪烁效果 */
@keyframes newRecordBlink {
    0%, 100% {
        color: #4ecdc4;
        text-shadow: 0 0 10px #4ecdc4;
    }
    50% {
        color: #f1c40f;
        text-shadow: 0 0 20px #f1c40f;
    }
}

/* 增强的主题系统 */
.theme-neon {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a0a2e 50%, #0f0f23 100%);
}

.theme-neon .game-container,
.theme-neon .menu-container {
    border: 1px solid #00ffff;
    box-shadow:
        0 0 40px rgba(0, 255, 255, 0.4),
        inset 0 1px 0 rgba(0, 255, 255, 0.2);
    background: rgba(0, 255, 255, 0.05);
}

.theme-neon .menu-btn, .theme-neon .mode-btn {
    background: linear-gradient(135deg, #ff006e 0%, #8338ec 100%);
    box-shadow: 0 10px 30px rgba(255, 0, 110, 0.4);
}

.theme-nature {
    background: linear-gradient(135deg, #2d5016 0%, #3e6b1f 50%, #4a7c23 100%);
}

.theme-nature .game-container,
.theme-nature .menu-container {
    border: 1px solid #8bc34a;
    box-shadow:
        0 0 40px rgba(139, 195, 74, 0.4),
        inset 0 1px 0 rgba(139, 195, 74, 0.2);
    background: rgba(139, 195, 74, 0.05);
}

.theme-nature .menu-btn, .theme-nature .mode-btn {
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
    box-shadow: 0 10px 30px rgba(76, 175, 80, 0.4);
}

.theme-cyberpunk {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a0a2e 50%, #2a0845 100%);
}

.theme-cyberpunk .game-container,
.theme-cyberpunk .menu-container {
    border: 1px solid #00ffff;
    box-shadow:
        0 0 40px rgba(0, 255, 255, 0.4),
        0 0 80px rgba(255, 0, 128, 0.2),
        inset 0 1px 0 rgba(0, 255, 255, 0.2);
    background: rgba(0, 255, 255, 0.03);
}

.theme-cyberpunk .menu-btn, .theme-cyberpunk .mode-btn {
    background: linear-gradient(135deg, #00ffff 0%, #ff0080 100%);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.4);
}

.theme-galaxy {
    background: linear-gradient(135deg, #1a0033 0%, #2d1b69 50%, #11998e 100%);
}

.theme-galaxy .game-container,
.theme-galaxy .menu-container {
    border: 1px solid #9b59b6;
    box-shadow:
        0 0 40px rgba(155, 89, 182, 0.4),
        0 0 80px rgba(52, 152, 219, 0.2),
        inset 0 1px 0 rgba(155, 89, 182, 0.2);
    background: rgba(155, 89, 182, 0.05);
}

.theme-galaxy .menu-btn, .theme-galaxy .mode-btn {
    background: linear-gradient(135deg, #9b59b6 0%, #3498db 100%);
    box-shadow: 0 10px 30px rgba(155, 89, 182, 0.4);
}

.theme-retro {
    background: linear-gradient(135deg, #2c1810 0%, #8b4513 50%, #d2691e 100%);
}

.theme-retro .game-container,
.theme-retro .menu-container {
    border: 1px solid #f39c12;
    box-shadow:
        0 0 40px rgba(243, 156, 18, 0.4),
        inset 0 1px 0 rgba(243, 156, 18, 0.2);
    background: rgba(243, 156, 18, 0.05);
}

.theme-retro .menu-btn, .theme-retro .mode-btn {
    background: linear-gradient(135deg, #f39c12 0%, #e74c3c 100%);
    box-shadow: 0 10px 30px rgba(243, 156, 18, 0.4);
}

/* 加载动画 */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.loading-screen::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 30% 20%, rgba(78, 205, 196, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(52, 152, 219, 0.1) 0%, transparent 50%);
    animation: loadingBg 4s ease-in-out infinite;
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
    transform: scale(1.1);
}

.loading-content {
    text-align: center;
    color: white;
    position: relative;
    z-index: 1;
    animation: fadeInUp 1s ease-out;
}

.snake-loader {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.snake-segment {
    width: 18px;
    height: 18px;
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    border-radius: 50%;
    animation: snakeMove 1.8s ease-in-out infinite;
    box-shadow: 0 0 20px rgba(78, 205, 196, 0.5);
    position: relative;
}

.snake-segment::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120%;
    height: 120%;
    background: inherit;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0.3;
    animation: pulse 2s ease-in-out infinite;
}

.snake-segment:nth-child(1) { animation-delay: 0s; }
.snake-segment:nth-child(2) { animation-delay: 0.2s; }
.snake-segment:nth-child(3) { animation-delay: 0.4s; }
.snake-segment:nth-child(4) { animation-delay: 0.6s; }

@keyframes snakeMove {
    0%, 100% {
        transform: translateY(0) scale(1);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-25px) scale(1.3);
        opacity: 1;
    }
}

@keyframes loadingBg {
    0%, 100% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.1); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.loading-content h2 {
    font-size: 2.8em;
    font-weight: 900;
    margin-bottom: 15px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #f39c12);
    background-size: 500% 500%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease-in-out infinite;
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
    letter-spacing: 2px;
}

.loading-content p {
    font-size: 1.3em;
    opacity: 0.9;
    animation: pulse 2.5s ease-in-out infinite;
    font-weight: 300;
    letter-spacing: 1px;
}

@keyframes pulse {
    0%, 100% { opacity: 0.7; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.02); }
}

/* 皮肤商店样式 */
.shop-content {
    text-align: center;
}

.currency-display {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 15px;
    margin-bottom: 20px;
    font-size: 1.2em;
}

.currency-value {
    color: #f1c40f;
    font-weight: bold;
    margin-left: 10px;
}

.skin-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.skin-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.skin-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.skin-item.selected {
    border-color: #2ecc71;
    background: rgba(46, 204, 113, 0.2);
}

.skin-item.locked {
    opacity: 0.6;
}

.skin-preview {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin: 0 auto 10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.skin-price {
    font-size: 0.9em;
    color: #f1c40f;
    margin-top: 5px;
}

/* 关卡编辑器样式 */
.level-editor-container {
    max-width: 800px;
    width: 95%;
}

.editor-controls {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
}

.tool-palette {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.tool-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.tool-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.tool-btn.active {
    background: #3498db;
    color: white;
}

.editor-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.action-btn {
    background: #27ae60;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.action-btn:hover {
    background: #2ecc71;
}

.editor-canvas-container {
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    overflow: hidden;
    margin: 20px 0;
}

/* 每日挑战样式 */
.challenge-info {
    text-align: center;
}

.challenge-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
}

.challenge-status {
    margin-top: 15px;
    padding: 10px;
    border-radius: 10px;
    font-weight: bold;
}

.challenge-status.completed {
    background: rgba(46, 204, 113, 0.3);
    color: #2ecc71;
}

.challenge-status.pending {
    background: rgba(241, 196, 15, 0.3);
    color: #f1c40f;
}

.challenge-rewards {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
}

.reward-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 15px;
}

.reward-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    border-radius: 10px;
    font-size: 0.9em;
}

.challenge-start-btn {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1em;
    font-weight: bold;
    cursor: pointer;
    margin: 20px 10px;
    transition: all 0.3s ease;
}

.challenge-start-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(231, 76, 60, 0.3);
}

/* 多人游戏样式 */
.multiplayer-scores {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
    gap: 20px;
}

.player-score {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 15px;
    flex: 1;
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(46, 204, 113, 0.9);
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    z-index: 10000;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 新特效动画 */
@keyframes magnetPulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(3);
        opacity: 0;
    }
}

@keyframes explosion {
    0% {
        transform: translate(0, 0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(var(--deltaX), var(--deltaY)) scale(0);
        opacity: 0;
    }
}

@keyframes collectEffect {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

@keyframes coinFloat {
    0% {
        transform: translateY(0);
        opacity: 1;
    }
    100% {
        transform: translateY(-30px);
        opacity: 0;
    }
}

@keyframes comboFloat {
    0% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    50% {
        transform: translateY(-20px) scale(1.2);
        opacity: 1;
    }
    100% {
        transform: translateY(-40px) scale(0.8);
        opacity: 0;
    }
}

@keyframes bossDefeat {
    0% {
        transform: translate(0, 0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(var(--deltaX), var(--deltaY)) scale(0);
        opacity: 0;
    }
}

/* 响应式设计改进 */
@media (max-width: 768px) {
    .skin-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }

    .editor-controls {
        flex-direction: column;
    }

    .tool-palette, .editor-actions {
        justify-content: center;
    }

    .multiplayer-scores {
        flex-direction: column;
    }
}

/* 成就系统样式 */
.achievements-content {
    text-align: center;
}

.achievement-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 15px;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 15px;
    flex: 1;
    min-width: 150px;
}

.stat-label {
    display: block;
    font-size: 0.9em;
    opacity: 0.8;
    margin-bottom: 5px;
}

.stat-value {
    display: block;
    font-size: 1.2em;
    font-weight: bold;
    color: #f1c40f;
}

.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.achievement-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
}

.achievement-item.completed {
    background: rgba(46, 204, 113, 0.2);
    border-color: #2ecc71;
}

.achievement-item.locked {
    opacity: 0.6;
}

.achievement-icon {
    font-size: 2em;
    min-width: 50px;
    text-align: center;
}

.achievement-info {
    flex: 1;
    text-align: left;
}

.achievement-info h4 {
    margin: 0 0 5px 0;
    font-size: 1.1em;
}

.achievement-info p {
    margin: 0 0 10px 0;
    font-size: 0.9em;
    opacity: 0.8;
}

.achievement-reward {
    font-size: 0.8em;
    color: #f1c40f;
    font-weight: bold;
}

.achievement-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 1.5em;
}

/* Boss战样式 */
.boss-selection {
    text-align: center;
}

.boss-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.boss-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.boss-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    border-color: #e74c3c;
}

.boss-avatar {
    font-size: 3em;
    margin-bottom: 15px;
}

.boss-card h3 {
    margin: 0 0 15px 0;
    color: #e74c3c;
}

.boss-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 15px;
    font-size: 0.9em;
}

.boss-abilities {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    justify-content: center;
}

.ability-tag {
    background: rgba(231, 76, 60, 0.3);
    color: #e74c3c;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 0.8em;
    border: 1px solid rgba(231, 76, 60, 0.5);
}

/* Boss血条样式 */
.boss-health-container {
    background: rgba(0, 0, 0, 0.8);
    padding: 15px;
    border-radius: 15px;
    margin: 10px 0;
    border: 2px solid #e74c3c;
}

.boss-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-weight: bold;
}

.boss-health-bar {
    position: relative;
    background: rgba(255, 255, 255, 0.2);
    height: 20px;
    border-radius: 10px;
    overflow: hidden;
}

.boss-health-fill {
    height: 100%;
    background: #e74c3c;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.boss-health-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.8em;
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
}

/* 连击效果样式 */
.combo-effect {
    font-family: 'Orbitron', monospace;
    text-transform: uppercase;
    letter-spacing: 2px;
}

/* 教程系统样式 */
.tutorial-container {
    max-width: 600px;
    width: 95%;
}

.tutorial-content {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 30px;
    margin: 20px 0;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tutorial-step {
    text-align: center;
}

.tutorial-image {
    font-size: 4em;
    margin-bottom: 20px;
}

.tutorial-step h3 {
    margin: 0 0 20px 0;
    color: #4ecdc4;
    font-size: 1.5em;
}

.tutorial-step p {
    font-size: 1.1em;
    line-height: 1.6;
    opacity: 0.9;
    margin: 0;
}

.tutorial-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
    gap: 15px;
}

.tutorial-btn {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    font-size: 1em;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
}

.tutorial-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(52, 152, 219, 0.3);
}

.tutorial-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.tutorial-btn.skip-btn {
    background: linear-gradient(45deg, #95a5a6, #7f8c8d);
}

.tutorial-btn.start-btn {
    background: linear-gradient(45deg, #27ae60, #2ecc71);
}

.tutorial-progress {
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 15px;
    border-radius: 15px;
    font-weight: bold;
    color: #f1c40f;
}

.tutorial-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin: 20px 0;
}

/* 实用CSS类 */
.glass-effect {
    background: var(--glass-bg);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-light);
}

.hover-lift {
    transition: var(--transition);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.glow-effect {
    animation: glow 2s ease-in-out infinite alternate;
}

.float-effect {
    animation: float 3s ease-in-out infinite;
}

.fade-in {
    animation: slideInUp 0.6s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

/* 改进的粒子效果 */
.particle-enhanced {
    position: absolute;
    width: 6px;
    height: 6px;
    background: radial-gradient(circle, var(--primary-color), transparent);
    border-radius: 50%;
    animation: particleFloatEnhanced 3s ease-out forwards;
    box-shadow: 0 0 10px var(--primary-color);
}

@keyframes particleFloatEnhanced {
    0% {
        opacity: 1;
        transform: scale(1) translateY(0) rotate(0deg);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.5) translateY(-30px) rotate(180deg);
    }
    100% {
        opacity: 0;
        transform: scale(0.5) translateY(-60px) rotate(360deg);
    }
}

/* 改进的通知样式 */
.notification-enhanced {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    color: white;
    padding: 20px 25px;
    border-radius: 15px;
    z-index: 10000;
    animation: slideInRight 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-heavy);
    max-width: 300px;
}

.notification-enhanced.success {
    border-left: 4px solid var(--success-color);
    box-shadow: 0 0 20px rgba(39, 174, 96, 0.3), var(--shadow-heavy);
}

.notification-enhanced.warning {
    border-left: 4px solid var(--warning-color);
    box-shadow: 0 0 20px rgba(243, 156, 18, 0.3), var(--shadow-heavy);
}

.notification-enhanced.error {
    border-left: 4px solid var(--accent-color);
    box-shadow: 0 0 20px rgba(231, 76, 60, 0.3), var(--shadow-heavy);
}

/* 响应式设计扩展 */
@media (max-width: 768px) {
    .achievements-grid {
        grid-template-columns: 1fr;
    }

    .achievement-item {
        flex-direction: column;
        text-align: center;
        padding: 15px;
    }

    .boss-grid {
        grid-template-columns: 1fr;
    }

    .achievement-stats {
        flex-direction: column;
    }

    .notification-enhanced {
        right: 10px;
        left: 10px;
        max-width: none;
        padding: 15px 20px;
    }

    .snake-loader {
        gap: 6px;
        padding: 15px;
    }

    .snake-segment {
        width: 15px;
        height: 15px;
    }
}

@media (max-width: 480px) {
    .loading-content h2 {
        font-size: 2.2em;
        letter-spacing: 1px;
    }

    .loading-content p {
        font-size: 1.1em;
    }

    .game-title::before {
        display: none; /* 在小屏幕上隐藏双重效果以提高性能 */
    }
}
